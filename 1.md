curl -X POST "http://turbotransitplatform.internal.sais.com.cn/turbotransitplatform/v1/backupjobs/" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer sk_123456789" \
  -H "user_group_token: a1b9c608f0059a83ec2cb17cc37b9dd2" \
  -d '{
    "source_path": "/cfff-4a8d9af84f66_HDD/public/database/fuxi/c88/history/era5_fc0_merged/1h/2025/20250611-12",
    "data_id": "400040",
    "data_version": 1,
    "delete_source": false
  }'



  wangjunyao@MacBook-Pro-5df97d49 ~ % curl -X POST "http://turbotransitplatform.internal.sais.com.cn/turbotransitplatform/v1/backupjobs/" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer sk_123456789" \
  -H "user_group_token: a1b9c608f0059a83ec2cb17cc37b9dd2" \
  -d '{
    "source_path": "/cfff-85cad58c20e7_HDD/public/dataplatform/liuxuefen/copy_data_logs",
    "data_id": "378",
    "data_version": 2,
    "delete_source": false
  }'
{"status":200,"msg":"Backup job created successfully","data":{"workflow_id":"6322899f-dec2-446c-8cec-e3bb9d46452c","source_path":"/cfff-85cad58c20e7_HDD/public/dataplatform/liuxuefen/copy_data_logs","dest_path":"/projects-HDD/turbotransitplatform/378/version-2/cfff-85cad58c20e7_HDD/public/dataplatform/liuxuefen/copy_data_logs"}}%                                       wangjunyao@MacBook-Pro-5df97d49 ~ % 



