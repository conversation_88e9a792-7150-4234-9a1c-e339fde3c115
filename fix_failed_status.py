#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
import datetime
import json
import logging
import pymysql
import requests
import argparse
from typing import List, Dict, Optional

# 创建日志目录
LOG_DIR = '/app/logs/data_migration'
LOG_FILE = 'fix_failed_status.log'
LOG_PATH = os.path.join(LOG_DIR, LOG_FILE)

# 确保日志目录存在
os.makedirs(LOG_DIR, exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(LOG_PATH)
    ]
)
logger = logging.getLogger('fix_failed_status')

# 数据库配置
DB_CONFIG = {
    'dev': {
        'host': '************',
        'port': 30000,
        'user': 'root',
        'password': '',
        'db': 'CZY_DATA_ASSETS_META_DEV'
    },
    'prod': {
        'host': 'kube-starrocks-fe-search.data-infra-prod-ns',
        'port': 9030,
        'user': 'root',
        'password': '',
        'db': 'CZY_DATA_ASSETS_META_PROD'
    }
}

# API配置
API_CONFIG = {
    'dev': {
        'backup_url': 'http://turbotransitplatform-dev.internal.sais.com.cn/turbotransitplatform/v1/backupjobs/',
        'auth_token': 'sk_123456789',
        'user_group_token': 'a1b9c608f0059a83ec2cb17cc37b9dd2'
    },
    'prod': {
        'backup_url': 'http://turbotransitplatform.internal.sais.com.cn/turbotransitplatform/v1/backupjobs/',
        'auth_token': 'sk_123456789',
        'user_group_token': 'a1b9c608f0059a83ec2cb17cc37b9dd2'
    }
}

class FailedStatusFixer:
    def __init__(self, env='dev', debug_mode=False, batch_size=50):
        self.env = env
        self.debug_mode = debug_mode
        self.batch_size = batch_size  # 批量处理大小
        self.db_config = DB_CONFIG[env]
        self.api_config = API_CONFIG[env]
        self.conn = None
        self.cursor = None
        
        # 统计信息
        self.total_failed_records = 0
        self.processed_count = 0
        self.success_count = 0
        self.still_failed_count = 0
        self.api_error_count = 0
        
        logger.info(f"初始化失败状态修正服务，环境: {env}, 调试模式: {'开启' if debug_mode else '关闭'}, 批量大小: {batch_size}")
        
    def connect_db(self):
        """连接数据库"""
        try:
            self.conn = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['db']
            )
            self.cursor = self.conn.cursor(pymysql.cursors.DictCursor)
            logger.info(f"成功连接到{self.env}环境数据库")
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise
    
    def close_db(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        logger.info("数据库连接已关闭")
    
    def get_failed_records(self) -> List[Dict]:
        """获取状态为failed的记录"""
        try:
            query = """
            SELECT id, workflow_id, dataset_id, source_path, error_message, cre_dt
            FROM data_migration_record 
            WHERE status = 'failed' 
            AND workflow_id IS NOT NULL 
            AND workflow_id != ''
            ORDER BY cre_dt DESC
            """
            self.cursor.execute(query)
            results = self.cursor.fetchall()
            logger.info(f"获取到{len(results)}条状态为failed的记录")
            return results
        except Exception as e:
            logger.error(f"获取失败记录失败: {str(e)}")
            raise
    
    def check_job_status(self, workflow_id: str) -> Optional[Dict]:
        """检查任务状态"""
        try:
            url = f"{self.api_config['backup_url']}?workflow_id={workflow_id}"
            headers = {
                'Accept': 'application/json',
                'Authorization': f"Bearer {self.api_config['auth_token']}",
                'user_group_token': self.api_config['user_group_token']
            }
            
            if self.debug_mode:
                logger.info(f"调试模式: 检查任务状态 (workflow_id={workflow_id})")
                # 模拟不同的状态结果
                import random
                statuses = ['completed', 'failed', 'running', 'pending']
                weights = [0.6, 0.2, 0.1, 0.1]  # 60%成功，20%失败，20%其他状态
                status = random.choices(statuses, weights=weights)[0]
                
                mock_result = {
                    'workflow_id': workflow_id,
                    'status': status,
                    'progress': '100%' if status == 'completed' else '50%',
                }
                
                if status == 'failed':
                    mock_result['error_message'] = '模拟的错误信息'
                
                logger.info(f"调试模式: 模拟任务状态: {mock_result}")
                return mock_result
            else:
                response = requests.get(url, headers=headers, timeout=30)
                response.raise_for_status()
                result = response.json()
                
                if result.get('status') == 200 and 'data' in result:
                    job_data = result['data']
                    logger.info(f"查询任务状态成功: workflow_id={workflow_id}, status={job_data.get('status')}, progress={job_data.get('progress')}")
                    return job_data
                else:
                    logger.error(f"查询任务状态失败: {result}")
                    return None
            
        except requests.exceptions.Timeout:
            logger.error(f"查询任务状态超时: workflow_id={workflow_id}")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"查询任务状态网络错误: workflow_id={workflow_id}, error={str(e)}")
            return None
        except Exception as e:
            logger.error(f"查询任务状态失败: workflow_id={workflow_id}, error={str(e)}")
            return None
    
    def update_record_status(self, record_id: int, status: str, progress: str = None, 
                           error_message: str = None, end_time: bool = False) -> bool:
        """更新记录状态"""
        try:
            update_fields = ["status = %s"]
            values = [status]
            
            if progress:
                update_fields.append("progress = %s")
                values.append(progress)
            
            if error_message:
                update_fields.append("error_message = %s")
                values.append(error_message)
            
            if end_time:
                update_fields.append("end_time = NOW()")
            
            update_fields.append("upd_dt = NOW()")
            
            query = f"UPDATE data_migration_record SET {', '.join(update_fields)} WHERE id = %s"
            values.append(record_id)
            
            if self.debug_mode:
                logger.info(f"调试模式: 更新记录状态 (ID={record_id}, status={status})")
                logger.info(f"调试模式: SQL: {query}")
                logger.info(f"调试模式: 参数: {values}")
                return True
            else:
                self.cursor.execute(query, values)
                self.conn.commit()
                logger.info(f"更新记录状态成功: ID={record_id}, status={status}")
                return True
            
        except Exception as e:
            if not self.debug_mode and self.conn:
                self.conn.rollback()
            logger.error(f"更新记录状态失败: ID={record_id}, error={str(e)}")
            return False
    
    def process_failed_records(self):
        """处理失败的记录"""
        try:
            # 获取失败的记录
            failed_records = self.get_failed_records()
            self.total_failed_records = len(failed_records)
            
            if self.total_failed_records == 0:
                logger.info("没有找到需要修正的失败记录")
                return
            
            logger.info(f"开始处理{self.total_failed_records}条失败记录")
            
            # 分批处理
            for i in range(0, len(failed_records), self.batch_size):
                batch = failed_records[i:i + self.batch_size]
                batch_num = i // self.batch_size + 1
                total_batches = (len(failed_records) + self.batch_size - 1) // self.batch_size
                
                logger.info(f"处理第{batch_num}/{total_batches}批，共{len(batch)}条记录")
                
                for record in batch:
                    self.process_single_record(record)
                    
                    # 批次间稍作休息，避免对API造成过大压力
                    if i + self.batch_size < len(failed_records):
                        time.sleep(1)
            
            # 输出处理结果统计
            self.print_summary()
            
        except Exception as e:
            logger.error(f"处理失败记录时发生错误: {str(e)}")
            raise
    
    def process_single_record(self, record: Dict):
        """处理单条记录"""
        record_id = record['id']
        workflow_id = record['workflow_id']
        dataset_id = record['dataset_id']
        source_path = record['source_path']
        
        try:
            logger.info(f"处理记录: ID={record_id}, workflow_id={workflow_id}, dataset_id={dataset_id}")
            
            # 查询当前任务状态
            job_status = self.check_job_status(workflow_id)
            
            if job_status is None:
                logger.warning(f"无法查询到任务状态: workflow_id={workflow_id}")
                self.api_error_count += 1
                self.processed_count += 1
                return
            
            # 获取状态信息
            current_status = job_status.get('status', '').lower()
            progress = job_status.get('progress', '0')
            error_msg = job_status.get('error_message', job_status.get('message', ''))
            
            # 根据查询到的状态更新记录
            if current_status == 'completed':
                # 任务实际已完成，更新为成功状态
                success = self.update_record_status(
                    record_id, 
                    status='completed', 
                    progress=progress, 
                    error_message=None,
                    end_time=True
                )
                if success:
                    self.success_count += 1
                    logger.info(f"记录{record_id}状态已修正为completed")
                
            elif current_status == 'failed':
                # 任务确实失败，更新错误信息
                success = self.update_record_status(
                    record_id, 
                    status='failed', 
                    progress=progress, 
                    error_message=error_msg,
                    end_time=True
                )
                if success:
                    self.still_failed_count += 1
                    logger.info(f"记录{record_id}确认为failed状态，已更新错误信息")
                
            else:
                # 任务还在进行中或其他状态，更新为当前状态
                success = self.update_record_status(
                    record_id, 
                    status=current_status, 
                    progress=progress, 
                    error_message=error_msg if error_msg else None
                )
                if success:
                    logger.info(f"记录{record_id}状态已更新为{current_status}")
            
            self.processed_count += 1
            
            # 显示进度
            if self.processed_count % 10 == 0:
                logger.info(f"处理进度: {self.processed_count}/{self.total_failed_records}")
            
            # 避免请求过于频繁
            time.sleep(0.5)
            
        except Exception as e:
            logger.error(f"处理记录{record_id}时发生错误: {str(e)}")
            self.processed_count += 1
    
    def print_summary(self):
        """打印处理结果统计"""
        logger.info("=" * 60)
        logger.info("失败状态修正任务执行总结")
        logger.info("=" * 60)
        logger.info(f"总失败记录数: {self.total_failed_records}")
        logger.info(f"已处理记录数: {self.processed_count}")
        logger.info(f"修正为成功状态: {self.success_count}")
        logger.info(f"确认仍为失败状态: {self.still_failed_count}")
        logger.info(f"API查询错误: {self.api_error_count}")
        
        if self.total_failed_records > 0:
            success_rate = (self.success_count / self.total_failed_records) * 100
            logger.info(f"修正成功率: {success_rate:.2f}%")
        
        logger.info("=" * 60)
    
    def run(self):
        """执行修正流程"""
        try:
            # 连接数据库
            self.connect_db()
            
            # 处理失败记录
            self.process_failed_records()
            
        except Exception as e:
            logger.error(f"执行修正流程失败: {str(e)}")
        finally:
            # 关闭数据库连接
            self.close_db()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='修正失败状态的数据迁移记录')
    parser.add_argument('--env', choices=['dev', 'prod'], default='dev', help='运行环境 (dev 或 prod)')
    parser.add_argument('--debug', action='store_true', help='调试模式，不实际调用API和更新数据库')
    parser.add_argument('--batch-size', type=int, default=50, help='批量处理大小，默认50')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO', help='日志级别')
    args = parser.parse_args()

    # 设置日志级别
    logging.getLogger('fix_failed_status').setLevel(getattr(logging, args.log_level))

    logger.info(f"开始执行失败状态修正任务")
    logger.info(f"环境: {args.env}, 调试模式: {'开启' if args.debug else '关闭'}, 批量大小: {args.batch_size}")

    fixer = FailedStatusFixer(env=args.env, debug_mode=args.debug, batch_size=args.batch_size)
    fixer.run()

    logger.info("失败状态修正任务执行完成")

if __name__ == '__main__':
    main()
