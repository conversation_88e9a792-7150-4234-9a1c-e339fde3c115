#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试API调用问题的脚本
"""

import requests
import json

# API配置
API_CONFIG = {
    'backup_url': 'http://turbotransitplatform.internal.sais.com.cn/turbotransitplatform/v1/backupjobs/',
    'auth_token': 'sk_123456789',
    'user_group_token': 'a1b9c608f0059a83ec2cb17cc37b9dd2'
}

def test_get_request():
    """测试GET请求（用户成功的curl命令）"""
    print("=== 测试GET请求 ===")
    url = API_CONFIG['backup_url']
    headers = {
        'Accept': 'application/json',
        'Authorization': f"Bearer {API_CONFIG['auth_token']}",
        'user_group_token': API_CONFIG['user_group_token']
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        if response.status_code == 200:
            result = response.json()
            print(f"响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"GET请求失败: {str(e)}")

def test_post_request():
    """测试POST请求（脚本中的调用方式）"""
    print("\n=== 测试POST请求 ===")
    url = API_CONFIG['backup_url']
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': f"Bearer {API_CONFIG['auth_token']}",
        'user_group_token': API_CONFIG['user_group_token']
    }
    
    payload = {
        'source_path': '/cfff-4a8d9af84f66_HDD/public/database/fuxi/c88/history/era5_fc0_merged/1h/2025/20250611-12',
        'data_id': '400040',
        'data_version': 1,
        'delete_source': False
    }
    
    print(f"请求URL: {url}")
    print(f"请求头: {json.dumps(headers, indent=2, ensure_ascii=False)}")
    print(f"请求体: {json.dumps(payload, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code != 200:
            print(f"错误详情: {response.reason}")
            
    except Exception as e:
        print(f"POST请求失败: {str(e)}")

def test_post_request_with_different_payload():
    """测试不同的POST请求格式"""
    print("\n=== 测试不同的POST请求格式 ===")
    url = API_CONFIG['backup_url']
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': f"Bearer {API_CONFIG['auth_token']}",
        'user_group_token': API_CONFIG['user_group_token']
    }
    
    # 尝试不同的payload格式
    payloads = [
        {
            'source_path': '/cfff-4a8d9af84f66_HDD/public/database/fuxi/c88/history/era5_fc0_merged/1h/2025/20250611-12',
            'data_id': 400040,  # 数字而不是字符串
            'data_version': 1,
            'delete_source': False
        },
        {
            'source_path': '/cfff-4a8d9af84f66_HDD/public/database/fuxi/c88/history/era5_fc0_merged/1h/2025/20250611-12',
            'dest_path': '/projects-HDD/turbotransitplatform/400040/version-1/cfff-4a8d9af84f66_HDD/public/database/fuxi/c88/history/era5_fc0_merged/1h/2025/20250611-12',
            'data_id': 400040,
            'data_version': 1,
            'delete_source': False
        }
    ]
    
    for i, payload in enumerate(payloads, 1):
        print(f"\n--- 尝试格式 {i} ---")
        print(f"请求体: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        try:
            response = requests.post(url, headers=headers, data=json.dumps(payload))
            print(f"状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                print("✅ 成功!")
                break
                
        except Exception as e:
            print(f"请求失败: {str(e)}")

if __name__ == '__main__':
    test_get_request()
    test_post_request()
    test_post_request_with_different_payload()
