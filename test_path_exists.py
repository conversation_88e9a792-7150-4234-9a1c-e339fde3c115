#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试路径是否存在的脚本
"""

import os
import requests
import json

def test_path_exists():
    """测试路径是否存在"""
    # 从日志中提取的一些路径
    test_paths = [
        "/cpfs01/projects-HDD/cfff-4a8d9af84f66_HDD/public/database/fuxi/c88/history/era5_fc0_merged/1h/2025/20250611-12",
        "/cpfs01/projects-HDD/cfff-4a8d9af84f66_HDD/public/database/fuxi/c88/history/era5_fc0_merged/1h/2025/20250601-12",
        "/cpfs01/projects-HDD/cfff-4a8d9af84f66_HDD/public/database/fuxi/c88/history/era5_fc0_merged/1h/2025/20250629-00"
    ]
    
    converted_paths = [
        "/cfff-4a8d9af84f66_HDD/public/database/fuxi/c88/history/era5_fc0_merged/1h/2025/20250611-12",
        "/cfff-4a8d9af84f66_HDD/public/database/fuxi/c88/history/era5_fc0_merged/1h/2025/20250601-12",
        "/cfff-4a8d9af84f66_HDD/public/database/fuxi/c88/history/era5_fc0_merged/1h/2025/20250629-00"
    ]
    
    print("=== 检查原始路径是否存在 ===")
    for path in test_paths:
        exists = os.path.exists(path)
        print(f"路径: {path}")
        print(f"存在: {exists}")
        if exists:
            try:
                files = os.listdir(path)
                complete_files = [f for f in files if f.endswith('.complete')]
                print(f"文件数: {len(files)}, .complete文件: {complete_files}")
            except Exception as e:
                print(f"读取目录失败: {e}")
        print("-" * 80)

def test_api_with_existing_path():
    """使用已知存在的路径测试API"""
    print("\n=== 使用已知存在的路径测试API ===")
    
    # 从用户成功的curl响应中获取的路径
    known_good_path = "/cfff-85cad58c20e7_HDD/public/dataplatform/liuxuefen/copy_data_logs"
    
    url = 'http://turbotransitplatform.internal.sais.com.cn/turbotransitplatform/v1/backupjobs/'
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer sk_123456789',
        'user_group_token': 'a1b9c608f0059a83ec2cb17cc37b9dd2'
    }
    
    payload = {
        'source_path': known_good_path,
        'data_id': '378',  # 从成功的响应中获取
        'data_version': 31,  # 递增版本号
        'delete_source': False
    }
    
    print(f"测试路径: {known_good_path}")
    print(f"请求体: {json.dumps(payload, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ API调用成功!")
        else:
            print("❌ API调用失败")
            
    except Exception as e:
        print(f"请求失败: {str(e)}")

if __name__ == '__main__':
    test_path_exists()
    test_api_with_existing_path()
